import { PrismaClient, Role } from "@prisma/client"
import { hash } from "bcryptjs"

const prisma = new PrismaClient()

const defaultRoles = [
  {
    id: "admin",
    name: "Admin User",
    description: "Full system access",
    permissions: {
      users: true,
      roles: true,
      database: true,
      settings: true,
    },
    isSystem: true,
  },
  {
    id: "accounting",
    name: "Accounting User",
    description: "Access to accounting features",
    permissions: {
      users: false,
      roles: false,
      database: true,
      settings: false,
    },
    isSystem: true,
  },
  {
    id: "logistics",
    name: "Logistics User",
    description: "Access to logistics features",
    permissions: {
      users: false,
      roles: false,
      database: true,
      settings: false,
    },
    isSystem: true,
  },
  {
    id: "finance",
    name: "Finance User",
    description: "Access to finance features",
    permissions: {
      users: false,
      roles: false,
      database: true,
      settings: false,
    },
    isSystem: true,
  },
  {
    id: "supply-chain",
    name: "Supply Chain User",
    description: "Access to supply chain features",
    permissions: {
      users: false,
      roles: false,
      database: true,
      settings: false,
    },
    isSystem: true,
  },
  {
    id: "sales",
    name: "Sales User",
    description: "Access to sales features",
    permissions: {
      users: false,
      roles: false,
      database: true,
      settings: false,
    },
    isSystem: true,
  },
]

const defaultUsers = [
  {
    name: "Admin User",
    email: "<EMAIL>",
    password: "admin123",
    role: "admin",
    isActive: true,
  },
  {
    name: "Accounting User",
    email: "<EMAIL>",
    password: "accounting123",
    role: "accounting",
    isActive: true,
  },
  {
    name: "Logistics User",
    email: "<EMAIL>",
    password: "logistics123",
    role: "logistics",
    isActive: true,
  },
  {
    name: "Finance User",
    email: "<EMAIL>",
    password: "finance123",
    role: "finance",
    isActive: true,
  },
  {
    name: "Supply Chain User",
    email: "<EMAIL>",
    password: "supplychain123",
    role: "supply-chain",
    isActive: true,
  },
  {
    name: "Sales User",
    email: "<EMAIL>",
    password: "sales123",
    role: "sales",
    isActive: true,
  },
]

async function main() {
  console.log("Start seeding...")

  const roles: Role[] = []

  // Create roles
  for (const role of defaultRoles) {
    const exists = await prisma.role.findUnique({
      where: { id: role.id },
    })

    if (!exists) {
      const roleResult = await prisma.role.create({
        data: {
          id: role.id,
          code: role.name.toLowerCase().replaceAll(" ", "-"),
          name: role.name,
          description: role.description,
          isSystem: role.isSystem,
        },
      })

      roles.push(roleResult)

      console.log(`Created role: ${role.name}`)
    } else {
      console.log(`Role already exists: ${role.name}`)
    }
  }

  // Create users
  for (const user of defaultUsers) {
    const exists = await prisma.user.findUnique({
      where: { email: user.email },
    })

    if (!exists) {
      const hashedPassword = await hash(user.password, 12)
      const randomRoleIndex = Math.floor(Math.random() * roles.length)
      const randomRole = roles[randomRoleIndex]

      if (!randomRole) continue

      await prisma.user.create({
        data: {
          name: user.name,
          email: user.email,
          password: hashedPassword,
          roleId: randomRole.id,
          isActive: user.isActive,
        },
      })
      console.log(`Created user: ${user.name}`)
    } else {
      console.log(`User already exists: ${user.email}`)
    }
  }

  console.log("Seeding finished.")
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
