{"name": "omega", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "pnpm dlx prisma generate && next build", "start": "next start", "lint": "next lint", "format": "pnpm dlx prettier --write .", "db:push": "pnpm dlx prisma db push", "db:pull": "pnpm dlx prisma db pull", "db:generate": "pnpm dlx prisma generate", "db:reset": "pnpm dlx prisma migrate reset --skip-generate && pnpm db-push", "db:studio": "pnpm dlx prisma studio", "email": "email dev --dir components/email --port 4000", "seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@auth/core": "^0.38.0", "@auth/prisma-adapter": "2.7.2", "@casl/ability": "6.4.0", "@casl/react": "3.1.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@prisma/extension-accelerate": "^1.3.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.1.8", "@syncfusion/ej2-base": "^29.1.36", "@syncfusion/ej2-buttons": "^29.1.34", "@syncfusion/ej2-calendars": "^29.1.35", "@syncfusion/ej2-lists": "^29.1.34", "@syncfusion/ej2-react-base": "^29.1.33", "@syncfusion/ej2-react-buttons": "^29.1.34", "@syncfusion/ej2-react-calendars": "^29.1.35", "@syncfusion/ej2-react-dropdowns": "^29.1.38", "@syncfusion/ej2-react-inputs": "^29.1.38", "@syncfusion/ej2-react-navigations": "^29.1.38", "@syncfusion/ej2-react-popups": "^29.1.37", "@syncfusion/ej2-react-schedule": "^29.1.38", "@syncfusion/ej2-schedule": "^29.1.38", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.21.3", "@types/ini": "^4.1.1", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/uuid": "^10.0.0", "alert": "^6.0.2", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "framer-motion": "^12.7.3", "immer": "^10.1.1", "ini": "^5.0.0", "jose": "^6.0.10", "lucide-react": "^0.483.0", "motion": "^12.6.3", "next": "^14", "next-auth": "5.0.0-beta.25", "next-safe-action": "^7.10.4", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "path": "^0.12.7", "qrcode": "^1.5.4", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-email": "^4.0.3", "react-error-boundary": "4.0.13", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "recharts": "^2.15.2", "resend": "^4.2.0", "shadcn": "^2.5.0", "sonner": "^2.0.3", "speakeasy": "^2.0.0", "tailwind-merge": "^3.0.2", "use-debounce": "^10.0.4", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^8", "eslint-config-next": "14.2.26", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.8.2", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5"}, "overrides": {"@radix-ui/react-dismissable-layer": "1.1.4"}, "pnpm": {"ignoredBuiltDependencies": ["@prisma/engines", "esbuild", "prisma"], "onlyBuiltDependencies": ["@prisma/client", "@prisma/engines", "esbuild", "prisma", "sharp"]}}