# CREATE DEVLOGS README.md HERE TO TRACK CHANGES

## Format
- [Filename/FilePath] (optional)
- [Date] [Action] [Description]

## Example
- [devlogs/README.md] [2025-05-28] [Fixed] [Fixed issue with devlogs README.md]
- [app/page.tsx] [2025-05-28] [Added] [Added new features to the user profile page]
- [components/ui/button.tsx] [2025-05-28] [Removed] [Removed the useless code]
- [prisma/schema.prisma] [2025-05-28] [Updated] [Updated the user roles]
- [GitHub] [2025-05-28] [Deployed] [Deployed the new version of the app]