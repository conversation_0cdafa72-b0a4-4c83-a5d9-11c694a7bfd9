datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

//** role - admin, sales, supply-chain, finance, logistics, accounting

model User {
  id            String    @id @default(uuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  password      String?
  roleId        String
  profileId     String?
  isOnline      Boolean   @default(false)
  isActive      Boolean   @default(true)
  lastActiveAt  DateTime?

  createdAt DateTime  @default(now())
  updatedAt DateTime  @default(now()) @updatedAt
  deletedAt DateTime?
  createdBy String?
  updatedBy String?
  deletedBy String?

  accounts Account[]
  profile  Profile?
  settings UserSettings?
  role     Role          @relation(fields: [roleId], references: [id])
}

model Profile {
  id     String @id @default(uuid())
  userId String @unique

  details Json?

  createdAt DateTime  @default(now())
  updatedAt DateTime  @default(now()) @updatedAt
  deletedAt DateTime?

  createdBy String?
  updatedBy String?
  deletedBy String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model UserSettings {
  id     String @id @default(uuid())
  userId String @unique

  // Dashboard settings
  dashboardSettings Json?

  // System settings
  systemSettings Json?

  // Role-specific settings
  roleSettings Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Settings {
  id        String   @id
  data      String   @db.Text // JSON string containing all settings
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?
  updatedBy String?
}

model RoleSettings {
  id       String @id @default(uuid())
  role     String @unique
  settings Json
}

model Role {
  id          String  @id @default(uuid())
  code        String  @unique
  name        String
  description String? @default("")
  isSystem    Boolean @default(false) // To mark built-in roles that can't be deleted

  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  createdBy String?
  updatedBy String?
  deletedBy String?

  permissions RolePermissions[]
  users       User[]
}

model Permission {
  id          String  @id @default(uuid())
  code        String  @unique
  name        String
  description String? @default("")

  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  createdBy String?
  updatedBy String?
  deletedBy String?

  permissions RolePermissions[]
}

model RolePermissions {
  roleId       String
  permissionId String
  actions      String[] @default([])

  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
}

model ActivityLog {
  id        String   @id @default(cuid())
  timestamp DateTime @default(now())
  user      String // User who performed the action
  action    String // Description of the action
  eventType String // user, system, security, data
  severity  String // info, warning, error, critical
  details   String // Additional details about the action
  ipAddress String? // IP address of the user
  userAgent String? // User agent of the browser
  metadata  Json? // Additional metadata as JSON
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([timestamp])
  @@index([user])
  @@index([eventType])
  @@index([severity])
}
