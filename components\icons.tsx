import {
  ArrowLeft,
  Check,
  CheckCircle2,
  CircleAlertIcon,
  Folder,
  Handshake,
  Inbox,
  Info,
  LayoutGrid,
  Loader2,
  LogIn,
  LucideIcon,
  MoonIcon,
  Settings,
  Sheet,
  SunIcon,
  TriangleAlert,
  Users,
  Watch,
  Plus,
  X,
  Search,
  RefreshCw,
  PlusCircle,
  Pencil,
  Trash,
  FileText,
  Lock,
  BarChart2,
  Calendar,
  History,
  Wallet,
  Truck,
  DollarSign,
  FolderClosed,
  FileBarChart2,
  Store,
  UserCheck,
  Receipt,
  PiggyBank,
  Paperclip,
  ClipboardEdit,
  ShoppingBag,
  ShoppingCart,
  Shapes,
  Box,
  Boxes,
  PackageCheck,
  FileEdit,
  Clock,
  ClipboardList,
  Copy,
  Shield,
  ShieldCheck,
  AlertCircle,
  QrCode,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsUpDown,
  MoreHorizontal,
  ChevronsLeft,
  ChevronsRight,
  ChevronsUp,
  ChevronsDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  EyeOff,
  Settings2,
  FunnelIcon,
} from "lucide-react"

export type Icon = LucideIcon

export const Icons = {
  qrCode: QrCode,
  watch: Watch,
  info: Info,
  triangleAlert: TriangleAlert,
  circleAlert: CircleAlertIcon,
  checkCircle: CheckCircle2,
  spinner: Loader2,
  sun: SunIcon,
  moon: MoonIcon,
  dashboard: LayoutGrid,
  users: Users,
  settings: Settings,
  table: Sheet,
  folder: Folder,
  handeShake: Handshake,
  shield: Shield,
  shieldCheck: ShieldCheck,
  alertCircle: AlertCircle,
  check: Check,
  inbox: Inbox,
  login: LogIn,
  plus: Plus,
  pencil: Pencil,
  trash: Trash,
  search: Search,
  refreshCw: RefreshCw,
  plusCircle: PlusCircle,
  x: X,
  fileText: FileText,
  lock: Lock,
  report: BarChart2,
  calendar: Calendar,
  history: History,
  wallet: Wallet,
  truck: Truck,
  cirleDollar: DollarSign,
  folderClosed: FolderClosed,
  squareActivity: FileBarChart2,
  store: Store,
  contact: UserCheck,
  receipt: Receipt,
  commision: PiggyBank,
  paperClip: Paperclip,
  notepadText: FileText,
  shoppingBag: ShoppingBag,
  shoppingCart: ShoppingCart,
  shapes: Shapes,
  boxes: Boxes,
  blocks: Box,
  shoppingBaskt: ShoppingBag,
  files: FileText,
  sheet: Sheet,
  clock: Clock,
  clipboardPen: ClipboardEdit,
  dollar: DollarSign,
  copy: Copy,
  chevUp: ChevronUp,
  chevDown: ChevronDown,
  chevLeft: ChevronLeft,
  chevRight: ChevronRight,
  chevUpDown: ChevronsUpDown,
  doubleChevLeft: ChevronsLeft,
  doubleChevRight: ChevronsRight,
  doubleChevUp: ChevronsUp,
  doubleChevDown: ChevronsDown,
  arrowUp: ArrowUp,
  arrowDown: ArrowDown,
  arrowLeft: ArrowLeft,
  arrowRight: ArrowRight,
  eyeOff: EyeOff,
  moreHorizontal: MoreHorizontal,
  adjustmentHorizontal: Settings2,
  funnel: FunnelIcon,
}
